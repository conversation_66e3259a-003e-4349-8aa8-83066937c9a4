package com.liyao.demo7;

public class Ex {
    public static void main(String[] args) {

        try {
            System.out.println(div(10, 2));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static int div(int a, int b) throws Exception {
        if (b == 0) {
            System.err.println("除数不能为0");
            throw new Exception("除数不能为0");
        }
        int result = a / b;
        return result;
    }
}

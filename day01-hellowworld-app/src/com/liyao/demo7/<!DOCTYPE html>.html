<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>仓库管理系统</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <style>
    body {
      margin: 0;
      background: #f6f6f8;
      font-family: "PingFang SC", Arial, sans-serif;
      color: #232323;
    }
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #fff;
      padding: 18px 16px 12px 16px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
      position: sticky;
      top: 0;
      z-index: 10;
    }
    .logo {
      width: 36px;
      height: 36px;
      background: url('logo.png') no-repeat center/cover;
      border-radius: 8px;
      margin-right: 10px;
    }
    .title {
      font-size: 1.2rem;
      font-weight: bold;
      letter-spacing: 1px;
      flex: 1;
    }
    .profile {
      display: flex;
      align-items: center;
    }
    .profile img {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      margin-left: 8px;
    }

    .overview {
      display: flex;
      justify-content: space-between;
      margin: 18px 12px 0 12px;
      gap: 10px;
    }
    .overview-card {
      flex: 1;
      background: #fffbe8;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(255,193,7,0.10);
      padding: 16px 0;
      text-align: center;
    }
    .overview-label {
      font-size: 0.9rem;
      color: #888;
    }
    .overview-value {
      font-size: 1.3rem;
      font-weight: bold;
      color: #d97706;
      margin-top: 6px;
    }

    .quick-actions {
      display: flex;
      justify-content: space-between;
      margin: 18px 12px 0 12px;
      gap: 8px;
    }
    .action-btn {
      flex: 1;
      padding: 12px 0;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 500;
      color: #fff;
      cursor: pointer;
      transition: box-shadow 0.2s;
      box-shadow: 0 2px 8px rgba(76,175,80,0.08);
      background: linear-gradient(90deg, #6c757d 0%, #495057 100%);
    }
    .action-in { background: linear-gradient(90deg, #f59e42 0%, #eab308 100%); }
    .action-out { background: linear-gradient(90deg, #4caf50 0%, #16a34a 100%); }
    .action-check { background: linear-gradient(90deg, #64748b 0%, #334155 100%); }
    .action-report { background: linear-gradient(90deg, #6366f1 0%, #2563eb 100%); }

    .alerts {
      margin: 18px 12px 0 12px;
      background: #fff;
      border-left: 5px solid #eab308;
      border-radius: 8px;
      padding: 10px 16px;
      font-size: 0.95rem;
      color: #b91c1c;
      box-shadow: 0 2px 8px rgba(255,193,7,0.04);
    }

    .charts, .warehouse-map, .news {
      margin: 18px 12px 0 12px;
      background: #fff;
      border-radius: 12px;
      padding: 18px;
      min-height: 120px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    }
    .charts img, .warehouse-map img {
      width: 100%;
      height: 100px;
      object-fit: contain;
      border-radius: 8px;
    }

    .news-title {
      font-size: 1rem;
      font-weight: bold;
      color: #d97706;
      margin-bottom: 8px;
    }
    .news-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    .news-list li {
      font-size: 0.95rem;
      color: #444;
      padding: 4px 0;
      border-bottom: 1px solid #f3f3f3;
    }
    .news-list li:last-child { border-bottom: none; }
    /* 响应式优化 */
    @media (max-width: 400px) {
      .overview, .quick-actions {
        flex-direction: column;
        gap: 6px;
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <div style="display:flex;align-items:center;">
      <div class="logo"></div>
      <span class="title">仓库管理系统</span>
    </div>
    <div class="profile">
      <span>管理员</span>
      <img src="user.png" alt="用户头像">
    </div>
  </div>

  <div class="overview">
    <div class="overview-card">
      <div class="overview-label">仓库数</div>
      <div class="overview-value">5</div>
    </div>
    <div class="overview-card">
      <div class="overview-label">库存总量</div>
      <div class="overview-value">12,350</div>
    </div>
    <div class="overview-card">
      <div class="overview-label">今日出入库</div>
      <div class="overview-value">+120 / -80</div>
    </div>
  </div>

  <div class="quick-actions">
    <button class="action-btn action-in">入库登记</button>
    <button class="action-btn action-out">出库登记</button>
    <button class="action-btn action-check">库存盘点</button>
    <button class="action-btn action-report">报表分析</button>
  </div>

  <div class="alerts">
    <strong>库存预警：</strong> 仓库A某商品库存低于安全线，请及时补货！
  </div>

  <div class="charts">
    <img src="chart-demo.png" alt="库存趋势图">
  </div>

  <div class="warehouse-map">
    <img src="map-demo.png" alt="仓库位置分布">
  </div>

  <div class="news">
    <div class="news-title">系统公告</div>
    <ul class="news-list">
      <li>【通知】本月盘点时间为6月25日，请提前准备。</li>
      <li>【升级】系统已支持扫码入库，操作更便捷。</li>
      <li>【提醒】请定期检查库存有效期，防止过期物品。</li>
    </ul>
  </div>
</body>
</html>

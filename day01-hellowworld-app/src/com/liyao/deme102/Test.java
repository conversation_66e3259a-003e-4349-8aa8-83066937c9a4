package com.liyao.deme102;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class Test {
    // public static void main(String[] args) {
    // calc();
    // }

    // public static void calc() {
    // List<String> locations = new ArrayList<>();

    // String[] names = {"玉龙雪山", "丽江古城", "大理古城", "洱海"};

    // Random random = new Random();
    // for (int i = 0; i <= 80; i++) {
    // locations.add(names[random.nextInt(names.length)]);
    // }

    // System.out.println(locations);

    // // 2.
    // Map<String, Integer> map = new HashMap<>();
    // for (String location : locations) {
    // map.put(location, map.getOrDefault(location, 0) + 1);
    // }

    // map.forEach((key, value) -> {
    // System.out.println(key + "=" + value);
    // });

    // }

    // public static void main(String[] args) {
    // System.out.println(f(5));
    // }

    // public static int f(int n) {
    // if (n == 1) {
    // return 1;
    // }
    // return n * f(n - 1);
    // }
    // public static void main(String[] args) {
    // // 导出文件
    // File file = new File("/Users/<USER>/Desktop/2-学习资料");
    // search(file, "java");
    // }

    // // 搜索
    // public static void search(File file, String name) {
    // if (file.isDirectory()) {
    // File[] files = file.listFiles();
    // for (File f : files) {
    // if (f.isDirectory()) {
    // search(f, name);
    // } else {
    // if (f.getName().contains(name)) {
    // System.out.println(f.getName());
    // }
    // }
    // }
    // } else {
    // System.out.println(file.getName());
    // }
    // }

    // public static void main(String[] args) throws Exception {
    // String str = "我爱你中国abc666";

    // byte[] bytes = str.getBytes("GBK");
    // System.out.println(Arrays.toString(bytes));

    // String str2 = new String(bytes, "GBK");
    // System.out.println(str2);
    // }

    // public static void main(String[] args) {
    // try {
    // copyFile(new File("day01-hellowworld-app/text.txt"), new
    // File("day01-hellowworld-app/text2.txt"));
    // } catch (Exception e) {
    // e.printStackTrace();
    // }
    // }

    // // 复制文件
    // public static void copyFile(File src, File dest) throws Exception {
    // FileInputStream fis = null;
    // FileOutputStream fos = null;
    // try {

    // fis = new FileInputStream(src);
    // fos = new FileOutputStream(dest);
    // byte[] bytes = new byte[1024];
    // int len;
    // while ((len = fis.read(bytes)) != -1) {
    // fos.write(bytes, 0, len);
    // }
    // System.out.println("复制成功");
    // } catch (Exception e) {
    // e.printStackTrace();
    // } finally {
    // if (fis != null) {
    // try {
    // fis.close();
    // } catch (IOException e) {
    // e.printStackTrace();
    // }
    // }
    // if (fos != null) {
    // try {
    // fos.close();
    // } catch (IOException e) {
    // e.printStackTrace();
    // }
    // }
    // }
    // }

    public static void main(String[] args) throws Exception {
        // 创建文件字符输入流于源文件接通
        try (
                FileReader fr = new FileReader("day01-hellowworld-app/text.txt")

        ) {
            char[] chars = new char[3];
            int len;
            while ((len = fr.read(chars)) != -1) {
                System.out.println(new String(chars, 0, len));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}

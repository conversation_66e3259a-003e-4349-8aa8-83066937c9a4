package com.liyao.demo101;

import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Collection;
import java.util.Set;
import java.util.TreeSet;

public class Test {
    public static void main(String[] args) {
        // Set<String> set = new LinkedHashSet<>();
        // set.add("1");
        // set.add("2");
        // set.add("5");
        // set.add("3");
        // set.add("4");
        // System.out.println(set);
        // Set<Te> tes = new TreeSet<>(new Comparator<Te>() {
        //     @Override
        //     public int compare(Te o1, Te o2) {
        //         return o1.getAge() - o2.getAge();
        //     }
        // });
        // tes.add(new Te("张三", 20, "10000"));
        // tes.add(new Te("李四", 21, "10000"));
        // tes.add(new Te("王五", 22, "10000"));
        // tes.add(new Te("赵六", 23, "10000"));
        // tes.add(new Te("孙七", 24, "10000"));
        // tes.add(new Te("周八", 25, "10000"));
        // tes.add(new Te("吴九", 26, "10000"));       
        // System.out.println(tes);

        Map<String, String> map = new HashMap<>() ;
        map.put("嫦娥", "10000");
        map.put("后羿", "10000");
        map.put("猪八戒", "10000");
        map.put("孙悟空", "10000");
        map.put("沙和尚", "10000");
        map.put("唐僧", "10000");
        map.put("白龙马", "10000");
        System.out.println(map);

        System.out.println(map.get("嫦娥"));
   

        System.err.println(map.containsKey("1"));

        System.err.println(map.isEmpty());

        System.err.println(map.size());

        // 获取所有键值
        Set<String> keys = map.keySet();
        for (String key : keys) {
            System.out.println(key);
        }
        // 获取所有值
        Collection<String> values = map.values();
        for (String value : values) {
            System.out.println(value);
        }

        // 获取所有键值对
        Set<Map.Entry<String, String>> entries = map.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            System.out.println(entry.getKey() + "=" + entry.getValue());    
        }

        Set<Map.Entry<String, String>> entries2 = map.entrySet();
        for (Map.Entry<String, String> entry : entries2) {
            System.out.println(entry.getKey() + "=" + entry.getValue());
        }

        map.forEach((key, value) -> {
            System.out.println(key + "=" + value);
        });
        
    }

}

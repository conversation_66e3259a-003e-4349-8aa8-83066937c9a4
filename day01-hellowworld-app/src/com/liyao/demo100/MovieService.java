package com.liyao.demo100;

import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

public class MovieService {
    private static List<Movie> movies = new ArrayList<>();
    private static Scanner scanner = new Scanner(System.in);

    public void start() {
        while (true) {
            System.out.println("欢迎使用电影管理系统");
            System.out.println("1. 上架");
            System.out.println("2. 下架");
            System.out.println("3. 查询");
            System.out.println("4. 封杀");
            System.out.println("5. 退出");
            System.out.println("6. 展示全部");
            System.out.println("7. 修改");
            System.out.println("请输入你的选择：");
            int choice = scanner.nextInt();
            switch (choice) {
                case 1:
                    // 上架
                    addMovie();
                    break;
                case 2:
                    // 下架
                    deleteMovie();
                    break;
                case 3:
                    // 查询
                    queryMovie();
                    break;
                case 4:
                    // 封杀
                    killMovie();
                    break;

                case 5:
                    // 退出
                    System.out.println("退出成功");
                    return;
                case 6:
                    // 展示全部
                    showAllMovie();
                    break;
                case 7:
                    // 修改
                    updateMovie();
                    break;
                default:
                    System.out.println("输入错误，请重新输入");
                    break;
            }
        }
    }

    // 上架
    public void addMovie() {
        Movie movie = new Movie();
        System.out.println("请输入电影名称：");
        movie.setName(scanner.next());
        System.out.println("请输入电影价格：");
        movie.setPrice(scanner.nextDouble());
        System.out.println("请输入电影演员：");
        movie.setActor(scanner.next());
        System.out.println("请输入电影评分：");
        movie.setScore(scanner.nextDouble());
        movies.add(movie);
        System.out.println("电影上架成功");
        System.out.println(movies);
    }

    // 查询
    public void queryMovie() {
        System.out.println("请输入电影名称：");
        String name = scanner.next();
        Movie movie = findMovie(name);
        if (movie != null) {
            System.out.println(movie);
        } else {
            System.out.println("没有找到该电影");
        }
    }

    // 下架
    public void deleteMovie() {
        System.out.println("请输入电影名称：");
        String name = scanner.next();
        Movie movie = findMovie(name);
        movies.remove(movie);
    }

    // 封杀
    public void killMovie() {
        System.out.println("请输入封杀某个明星：");
        String name = scanner.next();
        for (int i = 0; i < movies.size(); i++) {
            Movie movie = movies.get(i);
            if (movie.getActor().contains(name)) {
                System.out.println("封杀成功");
                movies.remove(movie);
                i--;
            }
        }
        System.out.println(movies);
    }

    // 展示全部
    public void showAllMovie() {
        for (Movie movie : movies) {
            System.out.println(movie);
        }
    }

    // 修改
    public void updateMovie() {
        System.out.println("请输入电影名称：");
        String name = scanner.next();
        Movie movie = findMovie(name);
        if (movie != null) {
            System.out.println("请输入电影名称：");
            movie.setName(scanner.next());
            System.out.println("请输入电影价格：");
            movie.setPrice(scanner.nextDouble());
            System.out.println("请输入电影演员：");
            movie.setActor(scanner.next());
            System.out.println("请输入电影评分：");
            movie.setScore(scanner.nextDouble());
            System.out.println("修改成功");
            System.out.println(movie);
        } else {
            System.out.println("没有找到该电影");
        }
    }
    public Movie findMovie(String name) {
        for (Movie movie : movies) {
            if (movie.getName().equals(name)) {
                return movie;
            }
        }
        return null;
    }
}

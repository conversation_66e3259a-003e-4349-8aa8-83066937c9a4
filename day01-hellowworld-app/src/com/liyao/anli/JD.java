package com.liyao.anli;

public class JD implements Switch{
    private String name;
    private boolean status;

    public JD() {
    }

    public JD(String name, boolean status) {
        this.name = name;
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean getStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    @Override
    public void press() {
        this.status = !this.status;
    }

}

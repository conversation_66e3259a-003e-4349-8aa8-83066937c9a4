package com.liyao.anli;

public class SmartHome {
    public void control(JD jd) {
        jd.press();
        System.out.println(jd.getName() + " " + jd.getStatus());
    }

    public void printAll(JD[] jds) {
        System.out.println("--------------------------------");
        System.out.println("全部的jd");
        System.out.println("--------------------------------");
        for (JD jd : jds) {
            System.out.println(jd.getName() + " " + jd.getStatus());
        }
    }
}

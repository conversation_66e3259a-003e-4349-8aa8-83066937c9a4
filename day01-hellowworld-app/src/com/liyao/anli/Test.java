package com.liyao.anli;

import java.util.Scanner;

public class Test {
    public static void main(String[] args) {
        JD[] jds = new JD[4];
        jds[1] = new TV("电视", true);
        jds[0] = new WashMachine("洗衣机", false);
        jds[2] = new Air("空调", true);
        jds[3] = new Lamp("灯", true);
        // for (JD jd : jds) {
        // System.out.println(jd.getName() + " " + jd.getStatus());
        // }
        // System.out.println("--------------------------------");

        SmartHome smartHome = new SmartHome();
        // smartHome.control(jds[0]);
        // smartHome.control(jds[1]);
        // smartHome.control(jds[2]);
        // smartHome.control(jds[3]);

        // 打印全部的jd

        while (true) {

            smartHome.printAll(jds);
            System.out.println("--------------------------------");
            System.out.println("请您选择您要控制的设备");
            Scanner sc = new Scanner(System.in);
            String index = sc.next();
            switch (index) {
                case "0":
                    smartHome.control(jds[0]);
                    break;
                case "1":
                    smartHome.control(jds[1]);
                    break;
                case "2":
                    smartHome.control(jds[2]);
                    break;
                case "3":
                    smartHome.control(jds[3]);
                    break;
                case "q":
                    System.out.println("退出");
                    return;
                default:
                    System.out.println("输入错误");
                    break;
            }
        }
    }
}

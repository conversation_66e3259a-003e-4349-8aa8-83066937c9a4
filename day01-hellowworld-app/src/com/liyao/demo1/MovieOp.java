package com.liyao.demo1;

import java.util.Scanner;

public class MovieOp {

    private Movie[] movies;

    public MovieOp(Movie[] arr) {
        this.movies = arr;
    }

    public void printAllMovie() {
        for (Movie movie : movies){
            System.out.printf(movie.getName() +  " " + movie.getPrice() + "\n" );
        }
    }

    // 根据id查询电影
    public void searchMovie() {
        System.out.println("请输入电影Id：");
        Scanner scanner = new Scanner(System.in);
        int id = scanner.nextInt();
        for (Movie movie : movies) {
            if (movie.getId() == id) {
                System.out.printf(movie.getName() +  " " + movie.getPrice() + "\n" );
                return;
            }
        }
        System.out.println("没有找到该电影");
    }
}

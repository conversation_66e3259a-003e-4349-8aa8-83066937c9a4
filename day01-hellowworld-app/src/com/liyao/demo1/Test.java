package com.liyao.demo1;

public class Test {
    public static void main(String[] args) {
        System.out.println("Hello World!");

        Movie[] arr = new Movie[6];
        arr[0] = new Movie(1, "唐顿庄园", 9.5, "罗宾·怀特"); // 修改为 double 类型
        arr[1] = new Movie(2, "唐顿庄园2", 9.6, "罗宾·怀特");
        arr[2] = new Movie(3, "唐顿庄园3", 9.7, "罗宾·怀特");
        arr[3] = new Movie(4, "唐顿庄园4", 9.8, "罗宾·怀特");
        arr[4] = new Movie(5, "唐顿庄园5", 9.9, "罗宾·怀特");
        arr[5] = new Movie(6, "唐顿庄园6", 10.0, "罗宾·怀特");


        MovieOp mo = new MovieOp(arr);
        mo.printAllMovie();

        System.out.println("--------------------------------------------------");
        mo.searchMovie();
    }
}

package com.liyao.demo8;

import java.util.ArrayList;
import java.util.List;

public class Colle {
    public static void main(String[] args) {

        List<String> names = new ArrayList<>();

        names.add("张三");
        names.add("李四");
        names.add("王五");
        names.add("赵六");

        System.out.println(names);

        names.add(3, "小六");

        System.err.println(names);

        names.remove(3);

        
        // Collection<String> names = new ArrayList<>();
        // names.add("张三");
        // names.add("李四");
        // names.add("王五");
        // names.add("赵六");
        // System.out.println(names);

        // for (String name : names) {
        // System.out.println(name);
        // }

        // Iterator<String> it = names.iterator();
        // while (it.hasNext()) {
        // String name = it.next();
        // System.out.println(name);
        // }
    }
}

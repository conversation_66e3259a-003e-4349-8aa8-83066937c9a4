package com.liyao.card;

public class Card {
    private int id;
    private String name;
    private double money;
    private String phone;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public double getMoney() {
        return money;
    }

    public void setMoney(double money) {
        this.money = money;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Override
    public String toString() {
        return "Card [id=" + id + ", name=" + name + ", money=" + money + ", phone=" + phone + "]";
    }

    // 无参方法
    public Card() {

    }

    // 有参方法
    public Card(int id, String name, double money, String phone) {
        this.id = id;
        this.name = name;
        this.money = money;
        this.phone = phone;
    }

    // 存金额
    public void saveMoney(double money) {
        this.money += money;
    }

    // 消费金额
    public void consumeMoney(double money) {
        this.money -= money;
    }


}

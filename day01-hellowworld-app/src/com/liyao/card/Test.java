package com.liyao.card;

import java.util.Scanner;

public class Test {
    public static void main(String[] args) {
        Card card = new Card(1, "张三", 5000, "13800138000");
        System.out.println(card);

        // 银卡
        Yk yk = new Yk(2, "李四", 5000, "13800138000");
        System.out.println(yk);

        main2(card);
        main2(yk);
    }

    public static void main2(Card card) {
        System.out.println("请刷卡，请输入当前消费的金额");
        Scanner sc = new Scanner(System.in);
        double money = sc.nextDouble();    
        card.consumeMoney(money);
        System.out.println("当前余额为：" + card.getMoney());

    }
}

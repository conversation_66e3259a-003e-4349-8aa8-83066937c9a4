package com.liyao.card;

public class gold extends Card {

    // 从写消费金额
    @Override
    public void consumeMoney(double money) {
        System.out.println("使用金卡消费，打8折");
        setMoney(getMoney() - money * 0.8);
        // 如果消费金额大于200，打印写车票
        if(money * 0.8 > 200) {
            printTicket();
        } else {
            System.out.println("消费金额小于200，不打印写车票");
        }
    }

    // 打印写车票
    public void printTicket() {
        System.out.println("打印写车票");
    }

    // 无参构造器
    public gold() {
    }

    // 有参构造器
    public gold(int id, String name, double money, String phone) {
        super(id, name, money, phone);
    }
}

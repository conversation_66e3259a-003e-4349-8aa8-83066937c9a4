package com.liyao.extendsmodifier;

public class Fu {
    private void show()
    {
        System.out.println("Fu show()");
    }

     void show2()
    {
        System.out.println("Fu show2()");
    }

    protected void show3()
    {
        System.out.println("Fu show3()");
    }

    public void show4()
    {
        System.out.println("Fu show4()");
    }

    public static void main(String[] args) {
        Fu fu = new Fu();
        fu.show();
        fu.show2();
        fu.show3();
        fu.show4();
    }
}


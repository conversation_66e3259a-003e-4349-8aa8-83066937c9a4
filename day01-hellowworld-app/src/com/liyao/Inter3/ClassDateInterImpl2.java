package com.liyao.Inter3;

import java.util.List;

public class ClassDateInterImpl2 implements ClassDI{

    private List<Stu> list;

    public ClassDateInterImpl2(List<Stu> list) {
        this.list = list;
    }
    @Override
    public void printAllStuInfos() {
        System.out.println("printAllStuInfos");
        for (Stu stu : list) {
            System.out.println(stu.getName() + " " + stu.getSex() + " " + stu.getScore());
        }
    }

    @Override
    public void printAuerScore() {
        System.out.println("printAuerScore");
        // 获取取平均分
        double sum = 0;
        for (Stu stu : list) {
            sum += stu.getScore();
        }
        double average = sum / list.size();
        System.out.println("平均分：" + average);
    }   
}

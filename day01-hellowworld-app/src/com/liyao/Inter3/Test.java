package com.liyao.Inter3;

import java.util.ArrayList;
import java.util.List;

public class Test {
    public static void main(String[] args) {
        List<Stu> list = new ArrayList<>();
        list.add(new Stu("张三", '男', 85.5));
        list.add(new Stu("李四", '女', 90.0));
        list.add(new Stu("王五", '男', 78.5));
        list.add(new Stu("赵六", '女', 88.0));
        list.add(new Stu("孙七", '男', 92.5));
        list.add(new Stu("周八", '女', 85.0));
        list.add(new Stu("吴九", '男', 80.5));
        list.add(new Stu("郑十", '女', 95.0));  
        list.add(new Stu("ss", '男', 85.5));
        list.add(new Stu("aa", '男', 85.5));


       

        ClassDateInterImpl1 classDateInterImpl1 = new ClassDateInterImpl1(list);
        classDateInterImpl1.printAllStuInfos();
        classDateInterImpl1.printAuerScore();

        ClassDateInterImpl2 classDateInterImpl2 = new ClassDateInterImpl2(list);
        classDateInterImpl2.printAllStuInfos();
        classDateInterImpl2.printAuerScore();

    }
}

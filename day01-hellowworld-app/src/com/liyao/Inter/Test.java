package com.liyao.Inter;

public class Test {
    public static void main(String[] args) {
        System.out.println(A.name);

    }
}

class C implements B,A{
  @Override
  public void show() {
    // TODO Auto-generated method stub
    throw new UnsupportedOperationException("Unimplemented method 'show'");
  } 

  @Override
  public void play() {
    // TODO Auto-generated method stub
    throw new UnsupportedOperationException("Unimplemented method 'show2'");
  }

  @Override
  public void show3() {
}

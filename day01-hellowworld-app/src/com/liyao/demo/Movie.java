package com.liyao.demo;

public class Movie {
    private int id; // 唯一标识
    private String name; // 电影名称
    private String price;
    private String actor;

    static String date;

    // 定义一个无参构造函数
    public Movie() {
    }

    // 定一个有参构造函数
    public Movie(int id, String name, String price, String actor) {
        this.id = id;
        this.name = name;
        this.price = price;
        this.actor = actor;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getActor() {
        return actor;
    }

    public void setActor(String actor) {
        this.actor = actor;
    }
}
